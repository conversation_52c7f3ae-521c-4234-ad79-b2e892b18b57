#include <windows.h>

VS_VERSION_INFO VERSIONINFO
	FILEVERSION 1,0,0,0
	PRODUCTVERSION 1,0,0,0
	FILEFLAGSMASK 0x3fL
#ifdef _DEBUG
	FILEFLAGS VS_FF_DEBUG
#else
	FILEFLAGS 0x0L
#endif
	FILEOS VOS__WINDOWS32
	FILETYPE VFT_DLL
	FILESUBTYPE 0x0L
	BEGIN
		BLOCK "StringFileInfo"
		BEGIN
			BLOCK "040904b0"
			BEGIN
				VALUE "CompanyName", "ADMotion Team\0"
				VALUE "FileDescription", "ADMotion直线插补测试程序(Qt控制台版)\0"
				VALUE "FileVersion", "*******\0"
				VALUE "LegalCopyright", "Copyright (c) 2024 ADMotion Team\0"
				VALUE "OriginalFilename", "test_interpolation_qt.exe\0"
				VALUE "ProductName", "ADMotion Line Interpolation Test\0"
				VALUE "ProductVersion", "*******\0"
			END
		END
		BLOCK "VarFileInfo"
		BEGIN
			VALUE "Translation", 0x0409, 1200
		END
	END
/* End of Version info */

