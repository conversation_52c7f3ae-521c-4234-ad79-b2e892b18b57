@echo off
echo =====================================
echo ADMotion MinGW 编译脚本
echo =====================================

:: 设置编码为UTF-8
chcp 65001 > nul

:: 检查是否存在必要的文件
echo 1. 检查必要文件...

if not exist "libADMotion.a" (
    echo 错误：未找到 libADMotion.a 文件
    echo 请确保该文件在当前目录下
    pause
    exit /b 1
)

if not exist "ADMotion.dll" (
    echo 错误：未找到 ADMotion.dll 文件
    echo 请确保该文件在当前目录下
    pause
    exit /b 1
)

echo 找到库文件: libADMotion.a
echo 找到DLL文件: ADMotion.dll

:: 检查MinGW编译器
echo.
echo 2. 检查MinGW编译器...
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误：未找到MinGW编译器 (g++)
    echo 请确保MinGW已安装并添加到PATH环境变量
    pause
    exit /b 1
)

g++ --version | findstr "g++"
echo MinGW编译器检查通过

:: 创建构建目录
echo.
echo 3. 创建构建目录...
if not exist build_mingw mkdir build_mingw
cd build_mingw

:: 使用专门的MinGW CMakeLists.txt
echo.
echo 4. 复制MinGW专用CMakeLists.txt...
copy ..\CMakeLists_mingw.txt CMakeLists.txt

:: 生成Makefile
echo.
echo 5. 生成Makefile...
cmake .. -G "MinGW Makefiles" -DCMAKE_BUILD_TYPE=Release
if %errorlevel% neq 0 (
    echo CMake生成失败！
    pause
    exit /b 1
)

:: 编译
echo.
echo 6. 开始编译...
mingw32-make
if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

:: 复制DLL到输出目录
echo.
echo 7. 复制DLL文件...
if exist "bin\test_line_interpolation.exe" (
    copy "..\ADMotion.dll" "bin\"
    echo DLL文件已复制到 bin\ 目录
    set EXE_PATH=bin\test_line_interpolation.exe
) else (
    echo 未找到可执行文件！
    pause
    exit /b 1
)

echo.
echo =====================================
echo 编译成功！
echo 可执行文件: %EXE_PATH%
echo =====================================

:: 询问是否运行
echo.
set /p choice="是否立即运行测试程序？(y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 8. 运行测试程序...
    echo 注意：请确保运动控制卡已连接并配置正确！
    echo.
    %EXE_PATH%
) else (
    echo.
    echo 编译完成！
    echo 可执行文件位于: %EXE_PATH%
    echo DLL文件已复制到相同目录
)

echo.
echo 完成！
pause
