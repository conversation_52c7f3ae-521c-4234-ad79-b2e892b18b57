@echo off
echo =====================================
echo ADMotion Qt直线插补测试编译脚本
echo =====================================

:: 设置编码为UTF-8
chcp 65001 > nul

:: 检查必要文件
echo 1. 检查必要文件...

if not exist "libADMotion.a" (
    echo 错误：未找到 libADMotion.a 文件
    pause
    exit /b 1
)

if not exist "ADMotion.dll" (
    echo 错误：未找到 ADMotion.dll 文件
    pause
    exit /b 1
)

if not exist "test_interpolation_qt.pro" (
    echo 错误：未找到 test_interpolation_qt.pro 文件
    pause
    exit /b 1
)

echo 所有必要文件检查通过

:: 检查Qt环境
echo.
echo 2. 检查Qt环境...
where qmake >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误：未找到qmake，请确保Qt已安装并添加到PATH
    echo 常见Qt路径示例：
    echo   C:\Qt\5.12.12\mingw73_64\bin
    echo   C:\Qt\5.15.2\mingw81_64\bin
    pause
    exit /b 1
)

qmake --version
echo Qt环境检查通过

:: 检查编译器
echo.
echo 3. 检查编译器...
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误：未找到g++编译器
    pause
    exit /b 1
)

echo 编译器检查通过

:: 清理旧的构建文件
echo.
echo 4. 清理旧的构建文件...
if exist Makefile del Makefile
if exist Makefile.Debug del Makefile.Debug
if exist Makefile.Release del Makefile.Release
if exist debug rmdir /s /q debug
if exist release rmdir /s /q release
if exist *.o del *.o

:: 生成Makefile
echo.
echo 5. 生成Makefile...
qmake test_interpolation_qt.pro
if %errorlevel% neq 0 (
    echo qmake生成失败！
    pause
    exit /b 1
)

echo Makefile生成成功

:: 编译项目
echo.
echo 6. 编译项目...
mingw32-make
if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

:: 检查可执行文件
echo.
echo 7. 检查编译结果...
if exist "release\test_interpolation_qt.exe" (
    set EXE_PATH=release\test_interpolation_qt.exe
    echo 发现Release版本: %EXE_PATH%
) else if exist "debug\test_interpolation_qt.exe" (
    set EXE_PATH=debug\test_interpolation_qt.exe
    echo 发现Debug版本: %EXE_PATH%
) else if exist "test_interpolation_qt.exe" (
    set EXE_PATH=test_interpolation_qt.exe
    echo 发现可执行文件: %EXE_PATH%
) else (
    echo 错误：未找到可执行文件！
    pause
    exit /b 1
)

:: 检查DLL是否已复制
for %%d in (release debug .) do (
    if exist "%%d\test_interpolation_qt.exe" (
        if not exist "%%d\ADMotion.dll" (
            echo 复制DLL到 %%d\ 目录...
            copy ADMotion.dll "%%d\"
        )
    )
)

echo.
echo =====================================
echo 编译成功！
echo 可执行文件: %EXE_PATH%
echo =====================================

:: 询问是否运行
echo.
set /p choice="是否立即运行Qt测试程序？(y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 8. 运行Qt测试程序...
    echo 注意：请确保运动控制卡已连接并配置正确！
    echo.
    %EXE_PATH%
) else (
    echo.
    echo 编译完成！
    echo 可执行文件位于: %EXE_PATH%
    echo DLL文件已复制到相同目录
)

echo.
echo 完成！
pause
