@echo off
echo =====================================
echo ADMotion 直接编译脚本 (不使用CMake)
echo =====================================

:: 设置编码为UTF-8
chcp 65001 > nul

:: 检查必要文件
echo 1. 检查必要文件...

if not exist "libADMotion.a" (
    echo 错误：未找到 libADMotion.a 文件
    pause
    exit /b 1
)

if not exist "ADMotion.dll" (
    echo 错误：未找到 ADMotion.dll 文件
    pause
    exit /b 1
)

if not exist "test_line_interpolation.cpp" (
    echo 错误：未找到 test_line_interpolation.cpp 文件
    pause
    exit /b 1
)

echo 所有必要文件检查通过

:: 检查编译器
echo.
echo 2. 检查编译器...
where g++ >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误：未找到 g++ 编译器
    pause
    exit /b 1
)

echo 编译器检查通过

:: 创建输出目录
echo.
echo 3. 创建输出目录...
if not exist output mkdir output

:: 编译主测试程序
echo.
echo 4. 编译主测试程序...
echo 编译命令: g++ -std=c++17 -I.. -o output/test_line_interpolation.exe test_line_interpolation.cpp libADMotion.a -lws2_32 -lwinmm

g++ -std=c++17 -I.. -o output/test_line_interpolation.exe test_line_interpolation.cpp libADMotion.a -lws2_32 -lwinmm

if %errorlevel% neq 0 (
    echo 主测试程序编译失败！
    pause
    exit /b 1
)

echo 主测试程序编译成功

:: 编译简化测试程序
echo.
echo 5. 编译简化测试程序...
echo 编译命令: g++ -std=c++17 -I.. -o output/simple_test.exe simple_test.cpp libADMotion.a -lws2_32 -lwinmm

g++ -std=c++17 -I.. -o output/simple_test.exe simple_test.cpp libADMotion.a -lws2_32 -lwinmm

if %errorlevel% neq 0 (
    echo 简化测试程序编译失败！
    pause
    exit /b 1
)

echo 简化测试程序编译成功

:: 复制DLL文件
echo.
echo 6. 复制DLL文件...
copy ADMotion.dll output\
echo DLL文件已复制到output目录

:: 显示结果
echo.
echo =====================================
echo 编译完成！
echo =====================================
echo 输出目录: output\
echo 主测试程序: output\test_line_interpolation.exe
echo 简化测试程序: output\simple_test.exe
echo DLL文件: output\ADMotion.dll
echo =====================================

:: 询问是否运行
echo.
set /p choice="选择要运行的程序 (1=主测试程序, 2=简化测试程序, n=不运行): "

if "%choice%"=="1" (
    echo.
    echo 运行主测试程序...
    cd output
    test_line_interpolation.exe
    cd ..
) else if "%choice%"=="2" (
    echo.
    echo 运行简化测试程序...
    cd output
    simple_test.exe
    cd ..
) else (
    echo.
    echo 编译完成！请手动运行程序。
)

echo.
echo 完成！
pause
