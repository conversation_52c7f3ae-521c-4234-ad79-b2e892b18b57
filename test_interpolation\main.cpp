/**
 * @file main.cpp
 * @brief ADMotion直线插补测试Qt控制台程序入口
 * <AUTHOR> Team
 * @date 2024-12-19
 *
 * Qt控制台版本的ADMotion直线插补测试程序
 * 支持Qt 5.12.12，使用C++17标准
 */

#include <QCoreApplication>
#include <QDir>
#include <QFile>
#include <QTextCodec>
#include <QDebug>
#include "linetest.h"

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);

    // 设置应用程序信息
    app.setApplicationName("ADMotion直线插补测试");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("ADMotion Team");
    app.setOrganizationDomain("admotion.com");

    // 设置中文编码支持（Qt5需要）
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
#endif

    qDebug() << "=== ADMotion 直线插补测试程序 (Qt版) ===";
    qDebug() << "测试内容: 四段直线插补（矩形路径）";
    qDebug() << "坐标系: 0, 轴: 0和1";
    qDebug() << "Qt版本:" << QT_VERSION_STR;
    qDebug() << "=======================================";

    // 检查DLL文件是否存在
    QString dllPath = QDir::currentPath() + "/ADMotion.dll";
    if (!QFile::exists(dllPath)) {
        qWarning() << "警告：未找到ADMotion.dll文件！";
        qWarning() << "请确保DLL文件位于：" << dllPath;
        qWarning() << "程序可能无法正常运行。";
    }

    // 创建测试对象
    LineTest test;

    // 连接完成信号到应用程序退出
    QObject::connect(&test, &LineTest::testFinished, &app, &QCoreApplication::quit);

    // 启动测试
    test.startTest();

    return app.exec();
}
