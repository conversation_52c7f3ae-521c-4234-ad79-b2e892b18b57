
/**
 * @file main.cpp
 * @brief ADMotion直线插补测试Qt控制台程序
 * <AUTHOR> Team
 * @date 2024-12-19
 *
 * Qt控制台版本的ADMotion直线插补测试程序
 * 支持Qt 5.12.12，使用C++17标准
 * 包含完整的测试流程
 */

#include <QCoreApplication>
#include <QDir>
#include <QFile>
#include <QTextCodec>
#include <QDebug>
#include <QTimer>
#include <QDateTime>
#include "../admc_pci.h"

// 常量定义
const short AXIS_STATUS_IN_POSITION = 1 << 4;  // 到位状态位 (16)
const short AXIS_STATUS_MOVING = 1 << 5;       // 运动中状态位 (32)
const short SUCCESS_CODE = 0;                  // 成功返回码

// 全局变量
TADMotionConn* g_handle = nullptr;
QTimer* g_timer = nullptr;
QCoreApplication* g_app = nullptr;
int g_currentStep = 0;
int g_axisCheckCount = 0;
QDateTime g_stepStartTime;

// 线段定义结构
struct LineSegment {
    int32_t x;
    int32_t y;
    QString name;

    LineSegment(int32_t x_val, int32_t y_val, const QString &name_val)
        : x(x_val), y(y_val), name(name_val) {}
};

QList<LineSegment> g_lineSegments;
int g_currentSegmentIndex = 0;

// 测试参数
const QString g_ip = "***********";
const int g_port = 6666;
const double g_maxVel = 3000.0;
const double g_maxAcc = 20.0;
const double g_lineVel = 1000.0;
const double g_lineAcc = 10.0;

// 函数声明
void logMessage(const QString &message);
void logError(const QString &error);
bool connectToBoard();
void disconnectFromBoard();
bool enableAxes();
bool setCoordinateParameters();
bool executeLineSegment(const LineSegment &segment);
bool checkAxesStatus();
void processNextStep();
void finishTest(bool success);

void logMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    qDebug() << QString("[%1] %2").arg(timestamp, message);
}

void logError(const QString &error)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    qCritical() << QString("[%1] 错误: %2").arg(timestamp, error);
}

bool connectToBoard()
{
    logMessage("1. 创建运动控制板卡句柄...");
    g_handle = API_CreateBoard();
    if (!g_handle) {
        logError("创建板卡句柄失败！");
        return false;
    }
    logMessage(QString("板卡句柄创建成功: %1").arg(reinterpret_cast<quintptr>(g_handle), 0, 16));

    logMessage("2. 连接运动控制板卡...");
    logMessage(QString("连接参数: IP=%1, 端口=%2").arg(g_ip).arg(g_port));

    short ret = API_OpenBoard(g_handle, g_ip.toLocal8Bit().data(), g_port);
    if (ret != SUCCESS_CODE) {
        logError(QString("连接板卡失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage("板卡连接成功！");
    return true;
}

void disconnectFromBoard()
{
    if (g_handle) {
        API_CloseBoard(g_handle);
        API_DeleteBoard(g_handle);
        g_handle = nullptr;
        logMessage("板卡连接已断开");
    }
}

bool enableAxes()
{
    logMessage("3. 使能轴...");

    // 使能轴0和轴1
    for (int i = 0; i < 2; i++) {
        short ret = API_AxisOn(g_handle, i);
        if (ret != SUCCESS_CODE) {
            logError(QString("使能轴%1失败！错误码: %2").arg(i).arg(ret));
            return false;
        }
        logMessage(QString("轴%1使能成功").arg(i));
    }

    logMessage("所有轴使能完成");
    return true;
}

bool setCoordinateParameters()
{
    logMessage("4. 设置坐标系参数...");
    short crd = 0;

    short ret = API_SetCrdPrm(g_handle, crd, g_maxVel, g_maxAcc);
    if (ret != SUCCESS_CODE) {
        logError(QString("设置坐标系参数失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage(QString("坐标系参数设置成功！坐标系: %1, 最大速度: %2, 最大加速度: %3")
               .arg(crd).arg(g_maxVel).arg(g_maxAcc));
    return true;
}

bool executeLineSegment(const LineSegment &segment)
{
    logMessage(QString("\n=== 执行%1 ===").arg(segment.name));
    logMessage(QString("目标坐标: (%1, %2), 速度: %3, 加速度: %4")
               .arg(segment.x).arg(segment.y).arg(g_lineVel).arg(g_lineAcc));

    // 发送直线插补指令
    short ret = API_Ln(g_handle, 0, segment.x, segment.y, g_lineVel, g_lineAcc);
    if (ret != SUCCESS_CODE) {
        logError(QString("发送直线插补指令失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage("直线插补指令发送成功");

    // 启动坐标系插补
    ret = API_CrdStart(g_handle, 0);
    if (ret != SUCCESS_CODE) {
        logError(QString("启动坐标系插补失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage("坐标系插补启动成功");

    return true;
}

int main(int argc, char *argv[])
{
    QCoreApplication app(argc, argv);
    g_app = &app;

    // 设置应用程序信息
    app.setApplicationName("ADMotion直线插补测试");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("ADMotion Team");
    app.setOrganizationDomain("admotion.com");

    // 设置中文编码支持（Qt5需要）
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
#endif

    qDebug() << "=== ADMotion 直线插补测试程序 (Qt版) ===";
    qDebug() << "测试内容: 四段直线插补（矩形路径）";
    qDebug() << "坐标系: 0, 轴: 0和1";
    qDebug() << "Qt版本:" << QT_VERSION_STR;
    qDebug() << "=======================================";

    // 检查DLL文件是否存在
    QString dllPath = QDir::currentPath() + "/ADMotion.dll";
    if (!QFile::exists(dllPath)) {
        qWarning() << "警告：未找到ADMotion.dll文件！";
        qWarning() << "请确保DLL文件位于：" << dllPath;
        qWarning() << "程序可能无法正常运行。";
    }

    // 初始化线段定义
    g_lineSegments.append(LineSegment(1000, 0, "第一段直线"));
    g_lineSegments.append(LineSegment(1000, 1000, "第二段直线"));
    g_lineSegments.append(LineSegment(0, 1000, "第三段直线"));
    g_lineSegments.append(LineSegment(0, 0, "第四段直线"));

    // 创建定时器
    g_timer = new QTimer(&app);
    QObject::connect(g_timer, &QTimer::timeout, processNextStep);

    // 开始测试
    logMessage("开始ADMotion直线插补测试...");
    g_currentStep = 1;
    g_timer->start(100); // 100ms后开始

    return app.exec();
}

bool checkAxesStatus()
{
    short axis0Status = 0, axis1Status = 0;
    short axis0 = 0, axis1 = 1; // 坐标系0对应轴0,1

    short ret0 = API_GetAxisStatus(g_handle, axis0, axis0Status);
    short ret1 = API_GetAxisStatus(g_handle, axis1, axis1Status);

    if (ret0 != SUCCESS_CODE || ret1 != SUCCESS_CODE) {
        logError(QString("获取轴状态失败！轴%1返回码: %2, 轴%3返回码: %4")
                 .arg(axis0).arg(ret0).arg(axis1).arg(ret1));
        return false;
    }

    // 检查两个轴是否都到位且不在运动中
    bool axis0Stopped = (axis0Status & AXIS_STATUS_IN_POSITION) && !(axis0Status & AXIS_STATUS_MOVING);
    bool axis1Stopped = (axis1Status & AXIS_STATUS_IN_POSITION) && !(axis1Status & AXIS_STATUS_MOVING);

    g_axisCheckCount++;
    if (g_axisCheckCount % 10 == 0) {  // 每1秒打印一次状态
        qint64 elapsedMs = g_stepStartTime.msecsTo(QDateTime::currentDateTime());
        logMessage(QString("检查次数: %1, 轴%2状态: 0x%3, 轴%4状态: 0x%5 (已用时: %6ms)")
                   .arg(g_axisCheckCount)
                   .arg(axis0).arg(axis0Status, 0, 16)
                   .arg(axis1).arg(axis1Status, 0, 16)
                   .arg(elapsedMs));
    }

    if (axis0Stopped && axis1Stopped) {
        qint64 elapsedMs = g_stepStartTime.msecsTo(QDateTime::currentDateTime());
        logMessage(QString("坐标系 0 的轴已停止！用时: %1ms").arg(elapsedMs));
        return true;
    }

    return false;
}

void processNextStep()
{
    switch (g_currentStep) {
    case 1: // 连接板卡
        g_timer->stop();
        if (connectToBoard()) {
            g_currentStep = 2;
            g_stepStartTime = QDateTime::currentDateTime();
            g_axisCheckCount = 0;
            logMessage("等待初始轴停止...");
            g_timer->start(100); // 100ms检查间隔
        } else {
            finishTest(false);
        }
        break;

    case 2: // 等待轴停止
        if (checkAxesStatus()) {
            g_timer->stop();
            g_currentStep = 3;
            g_timer->start(500); // 延迟500ms
        } else if (g_axisCheckCount >= 300) { // 30秒超时
            g_timer->stop();
            logError("等待轴停止超时！");
            finishTest(false);
        }
        break;

    case 3: // 使能轴
        g_timer->stop();
        if (enableAxes()) {
            g_currentStep = 4;
            g_timer->start(500);
        } else {
            finishTest(false);
        }
        break;

    case 4: // 设置坐标系参数
        g_timer->stop();
        if (setCoordinateParameters()) {
            g_currentStep = 5;
            g_currentSegmentIndex = 0;
            logMessage("5. 开始执行四段直线插补...");
            if (executeLineSegment(g_lineSegments[g_currentSegmentIndex])) {
                g_stepStartTime = QDateTime::currentDateTime();
                g_axisCheckCount = 0;
                g_timer->start(100);
            } else {
                finishTest(false);
            }
        } else {
            finishTest(false);
        }
        break;

    case 5: // 执行线段1
    case 6: // 执行线段2
    case 7: // 执行线段3
    case 8: // 执行线段4
        if (checkAxesStatus()) {
            g_timer->stop();
            logMessage(QString("%1 执行完成！").arg(g_lineSegments[g_currentSegmentIndex].name));

            g_currentSegmentIndex++;
            if (g_currentSegmentIndex < g_lineSegments.size()) {
                g_currentStep++;
                if (executeLineSegment(g_lineSegments[g_currentSegmentIndex])) {
                    g_stepStartTime = QDateTime::currentDateTime();
                    g_axisCheckCount = 0;
                    g_timer->start(100);
                } else {
                    finishTest(false);
                }
            } else {
                finishTest(true); // 所有线段完成
            }
        } else if (g_axisCheckCount >= 300) { // 30秒超时
            g_timer->stop();
            logError(QString("%1 执行超时！").arg(g_lineSegments[g_currentSegmentIndex].name));
            finishTest(false);
        }
        break;

    default:
        g_timer->stop();
        finishTest(false);
        break;
    }
}

void finishTest(bool success)
{
    if (g_timer) {
        g_timer->stop();
    }

    disconnectFromBoard();

    if (success) {
        logMessage("\n=== 所有直线插补测试完成！===");
        logMessage("测试结果: 成功完成四段直线插补");
    } else {
        logMessage("\n=== 测试失败！===");
    }

    logMessage("资源清理完成");
    logMessage("程序即将退出...");

    // 延迟1秒后退出
    QTimer::singleShot(1000, g_app, &QCoreApplication::quit);
}


