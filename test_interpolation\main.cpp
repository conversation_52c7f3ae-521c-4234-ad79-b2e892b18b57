/**
 * @file main.cpp
 * @brief ADMotion直线插补测试Qt应用程序入口
 * <AUTHOR> Team
 * @date 2024-12-19
 * 
 * Qt版本的ADMotion直线插补测试程序
 * 支持Qt 5.12.12，使用C++17标准
 */

#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QMessageBox>
#include <QTextCodec>
#include "mainwindow.h"

int main(int argc, char *argv[])
{
    QApplication app(argc, argv);
    
    // 设置应用程序信息
    app.setApplicationName("ADMotion直线插补测试");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("ADMotion Team");
    app.setOrganizationDomain("admotion.com");
    
    // 设置中文编码支持（Qt5需要）
#if QT_VERSION < QT_VERSION_CHECK(6, 0, 0)
    QTextCodec::setCodecForLocale(QTextCodec::codecForName("UTF-8"));
#endif
    
    // 设置应用程序样式
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // 设置应用程序图标（如果有的话）
    // app.setWindowIcon(QIcon(":/icons/app_icon.png"));
    
    // 检查DLL文件是否存在
    QString dllPath = QDir::currentPath() + "/ADMotion.dll";
    if (!QFile::exists(dllPath)) {
        QMessageBox::warning(nullptr, "警告", 
            QString("未找到ADMotion.dll文件！\n"
                   "请确保DLL文件位于：%1\n\n"
                   "程序可能无法正常运行。").arg(dllPath));
    }
    
    // 创建并显示主窗口
    MainWindow window;
    window.show();
    
    return app.exec();
}
