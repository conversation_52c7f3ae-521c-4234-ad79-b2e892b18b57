/**
 * @file test_line_interpolation.cpp
 * @brief 直线插补测试案例
 * <AUTHOR> Team
 * @date 2024-12-19
 * 
 * 测试功能：
 * 1. 连接运动控制卡
 * 2. 等待坐标系轴停止
 * 3. 设置坐标系参数
 * 4. 执行四段直线插补（矩形路径）
 * 5. 监控运动状态
 */

#include <iostream>
#include <chrono>
#include <thread>
#include <iomanip>
#include "../admc_pci.h"

// 在文件头加载DLL（用户需要手动复制DLL到当前文件夹）
#pragma comment(lib, "ADMotion.dll")

using namespace std;

// 轴状态位定义
const short AXIS_STATUS_IN_POSITION = 1 << 4;  // 到位状态位 (16)
const short AXIS_STATUS_MOVING = 1 << 5;       // 运动中状态位 (32)

// 错误码定义
const short SUCCESS_CODE = 0;  // 成功返回码

/**
 * @brief 等待坐标系轴停止函数
 * @param handle 运动控制句柄
 * @param crd 坐标系编号
 * @param timeoutMs 超时时间（毫秒）
 * @return true-轴已停止，false-超时或错误
 */
bool waitForAxesStop(TADMotionConn* handle, short crd, int timeoutMs = 30000) {
    cout << "等待坐标系 " << crd << " 的轴停止运动..." << endl;
    
    auto startTime = chrono::steady_clock::now();
    int checkCount = 0;
    
    while (true) {
        // 检查超时
        auto currentTime = chrono::steady_clock::now();
        auto elapsedMs = chrono::duration_cast<chrono::milliseconds>(currentTime - startTime).count();
        
        if (elapsedMs >= timeoutMs) {
            cout << "等待轴停止超时！超时时间: " << timeoutMs << "ms" << endl;
            return false;
        }
        
        // 检查坐标系0的两个轴状态（轴0和轴1）
        short axis0Status = 0, axis1Status = 0;
        short axis0 = crd * 2;      // 坐标系0对应轴0,1
        short axis1 = crd * 2 + 1;
        
        short ret0 = API_GetAxisStatus(handle, axis0, axis0Status);
        short ret1 = API_GetAxisStatus(handle, axis1, axis1Status);
        
        if (ret0 != SUCCESS_CODE || ret1 != SUCCESS_CODE) {
            cout << "获取轴状态失败！轴" << axis0 << "返回码: " << ret0 
                 << ", 轴" << axis1 << "返回码: " << ret1 << endl;
            return false;
        }
        
        // 检查两个轴是否都到位且不在运动中
        bool axis0Stopped = (axis0Status & AXIS_STATUS_IN_POSITION) && !(axis0Status & AXIS_STATUS_MOVING);
        bool axis1Stopped = (axis1Status & AXIS_STATUS_IN_POSITION) && !(axis1Status & AXIS_STATUS_MOVING);
        
        checkCount++;
        if (checkCount % 10 == 0) {  // 每1秒打印一次状态
            cout << "检查次数: " << checkCount 
                 << ", 轴" << axis0 << "状态: 0x" << hex << axis0Status 
                 << ", 轴" << axis1 << "状态: 0x" << axis1Status << dec
                 << " (已用时: " << elapsedMs << "ms)" << endl;
        }
        
        if (axis0Stopped && axis1Stopped) {
            cout << "坐标系 " << crd << " 的轴已停止！用时: " << elapsedMs << "ms" << endl;
            return true;
        }
        
        // 等待100ms后再次检查
        this_thread::sleep_for(chrono::milliseconds(100));
    }
}

/**
 * @brief 执行直线插补并等待完成
 * @param handle 运动控制句柄
 * @param crd 坐标系编号
 * @param x 目标X坐标
 * @param y 目标Y坐标
 * @param vel 插补速度
 * @param acc 插补加速度
 * @param segmentName 线段名称
 * @return true-成功，false-失败
 */
bool executeLineSegment(TADMotionConn* handle, short crd, int32_t x, int32_t y, 
                       double vel, double acc, const string& segmentName) {
    cout << "\n=== 执行" << segmentName << " ===" << endl;
    cout << "目标坐标: (" << x << ", " << y << "), 速度: " << vel << ", 加速度: " << acc << endl;
    
    // 发送直线插补指令
    short ret = API_Ln(handle, crd, x, y, vel, acc);
    if (ret != SUCCESS_CODE) {
        cout << "发送直线插补指令失败！错误码: " << ret << endl;
        return false;
    }
    cout << "直线插补指令发送成功" << endl;
    
    // 启动坐标系插补
    ret = API_CrdStart(handle, crd);
    if (ret != SUCCESS_CODE) {
        cout << "启动坐标系插补失败！错误码: " << ret << endl;
        return false;
    }
    cout << "坐标系插补启动成功" << endl;
    
    // 等待运动完成
    if (!waitForAxesStop(handle, crd)) {
        cout << segmentName << " 执行失败或超时！" << endl;
        return false;
    }
    
    cout << segmentName << " 执行完成！" << endl;
    return true;
}

/**
 * @brief 主测试函数
 */
int main() {
    cout << "=== ADMotion 直线插补测试程序 ===" << endl;
    cout << "测试内容: 四段直线插补（矩形路径）" << endl;
    cout << "坐标系: 0, 轴: 0和1" << endl;
    cout << "===============================" << endl;
    
    // 1. 创建板卡句柄
    cout << "\n1. 创建运动控制板卡句柄..." << endl;
    TADMotionConn* handle = API_CreateBoard();
    if (!handle) {
        cout << "创建板卡句柄失败！" << endl;
        return -1;
    }
    cout << "板卡句柄创建成功: " << handle << endl;
    
    // 2. 连接板卡
    cout << "\n2. 连接运动控制板卡..." << endl;
    const char* ip = "***********";
    int port = 6666;
    cout << "连接参数: IP=" << ip << ", 端口=" << port << endl;
    
    short ret = API_OpenBoard(handle, ip, port);
    if (ret != SUCCESS_CODE) {
        cout << "连接板卡失败！错误码: " << ret << endl;
        API_DeleteBoard(handle);
        return -1;
    }
    cout << "板卡连接成功！" << endl;
    
    // 3. 等待初始轴停止
    cout << "\n3. 等待初始轴停止..." << endl;
    if (!waitForAxesStop(handle, 0)) {
        cout << "初始轴停止检查失败！" << endl;
        API_CloseBoard(handle);
        API_DeleteBoard(handle);
        return -1;
    }
    
    // 4. 设置坐标系参数
    cout << "\n4. 设置坐标系参数..." << endl;
    short crd = 0;
    double maxVel = 3000.0;  // 最大速度
    double maxAcc = 20.0;    // 最大加速度
    
    ret = API_SetCrdPrm(handle, crd, maxVel, maxAcc);
    if (ret != SUCCESS_CODE) {
        cout << "设置坐标系参数失败！错误码: " << ret << endl;
        API_CloseBoard(handle);
        API_DeleteBoard(handle);
        return -1;
    }
    cout << "坐标系参数设置成功！坐标系: " << crd 
         << ", 最大速度: " << maxVel << ", 最大加速度: " << maxAcc << endl;
    
    // 5. 执行四段直线插补
    cout << "\n5. 开始执行四段直线插补..." << endl;
    
    // 插补参数
    double lineVel = 1000.0;  // 直线速度
    double lineAcc = 10.0;    // 直线加速度
    
    // 第一段：从原点到(1000, 0)
    if (!executeLineSegment(handle, crd, 1000, 0, lineVel, lineAcc, "第一段直线")) {
        goto cleanup;
    }
    
    // 第二段：从(1000, 0)到(1000, 1000)
    if (!executeLineSegment(handle, crd, 1000, 1000, lineVel, lineAcc, "第二段直线")) {
        goto cleanup;
    }
    
    // 第三段：从(1000, 1000)到(0, 1000)
    if (!executeLineSegment(handle, crd, 0, 1000, lineVel, lineAcc, "第三段直线")) {
        goto cleanup;
    }
    
    // 第四段：从(0, 1000)到(0, 0)
    if (!executeLineSegment(handle, crd, 0, 0, lineVel, lineAcc, "第四段直线")) {
        goto cleanup;
    }
    
    cout << "\n=== 所有直线插补测试完成！===" << endl;
    cout << "测试结果: 成功完成四段直线插补" << endl;
    
cleanup:
    // 6. 清理资源
    cout << "\n6. 清理资源..." << endl;
    API_CloseBoard(handle);
    API_DeleteBoard(handle);
    cout << "资源清理完成" << endl;
    
    cout << "\n程序执行完毕，按任意键退出..." << endl;
    cin.get();
    
    return 0;
}
