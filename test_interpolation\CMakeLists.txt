# CMakeLists.txt for ADMotion Line Interpolation Test
cmake_minimum_required(VERSION 3.10)

project(ADMotionLineInterpolationTest)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译器标志
if(MSVC)
    # Windows MSVC编译器设置
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} /Od /Zi")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /O2")
else()
    # GCC/Clang编译器设置
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
    set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
    set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2")
endif()

# 包含头文件目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)

# 查找ADMotion库文件
set(ADMOTION_LIB_DIR ${CMAKE_CURRENT_SOURCE_DIR})

# 根据编译器类型查找不同的库文件
if(MSVC)
    # MSVC编译器使用.lib文件
    find_library(ADMOTION_LIB
        NAMES ADMotion admc_pci
        PATHS ${ADMOTION_LIB_DIR} ${CMAKE_CURRENT_SOURCE_DIR}/..
        PATH_SUFFIXES lib Release Debug
        NO_DEFAULT_PATH
    )
else()
    # MinGW编译器使用.dll.a或.a文件
    find_library(ADMOTION_LIB
        NAMES libADMotion.a ADMotion.a libADMotion.dll.a ADMotion.dll.a
              libadmc_pci.a admc_pci.a libadmc_pci.dll.a admc_pci.dll.a
        PATHS ${ADMOTION_LIB_DIR} ${CMAKE_CURRENT_SOURCE_DIR}/..
        PATH_SUFFIXES . lib Release Debug
        NO_DEFAULT_PATH
    )
endif()

# 创建可执行文件
add_executable(test_line_interpolation
    test_line_interpolation.cpp
)

# 链接库文件
if(ADMOTION_LIB)
    message(STATUS "Found ADMotion library: ${ADMOTION_LIB}")
    target_link_libraries(test_line_interpolation ${ADMOTION_LIB})
else()
    message(WARNING "ADMotion library not found. You may need to:")
    message(WARNING "1. Copy ADMotion.dll.a or admc_pci.dll.a to the project directory")
    message(WARNING "2. Or build the ADMotion library first")

    # 尝试直接链接DLL（对于MinGW）
    if(NOT MSVC)
        # 查找DLL文件
        find_file(ADMOTION_DLL
            NAMES ADMotion.dll admc_pci.dll
            PATHS ${CMAKE_CURRENT_SOURCE_DIR} ${ADMOTION_LIB_DIR}
            NO_DEFAULT_PATH
        )

        if(ADMOTION_DLL)
            message(STATUS "Found ADMotion DLL: ${ADMOTION_DLL}")
            target_link_libraries(test_line_interpolation ${ADMOTION_DLL})
        endif()
    endif()
endif()

# Windows平台特定设置
if(WIN32)
    # 链接Windows系统库
    target_link_libraries(test_line_interpolation
        ws2_32
        winmm
    )

    # 设置控制台应用程序
    set_target_properties(test_line_interpolation PROPERTIES
        WIN32_EXECUTABLE FALSE
    )
endif()

# 设置输出目录
set_target_properties(test_line_interpolation PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 打印构建信息
message(STATUS "Building ADMotion Line Interpolation Test")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Compiler: ${CMAKE_CXX_COMPILER_ID}")
