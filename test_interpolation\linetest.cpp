/**
 * @file linetest.cpp
 * @brief 直线插补测试类实现
 * <AUTHOR> Team
 * @date 2024-12-19
 */

#include "linetest.h"

LineTest::LineTest(QObject *parent)
    : QObject(parent)
    , m_currentState(TestState::Idle)
    , m_handle(nullptr)
    , m_ip("***********")
    , m_port(6666)
    , m_maxVel(3000.0)
    , m_maxAcc(20.0)
    , m_lineVel(1000.0)
    , m_lineAcc(10.0)
    , m_currentSegmentIndex(0)
    , m_axisCheckCount(0)
    , m_maxAxisCheckCount(300) // 30秒超时 (300 * 100ms)
{
    // 初始化定时器
    m_axisCheckTimer = new QTimer(this);
    m_axisCheckTimer->setSingleShot(false);
    m_axisCheckTimer->setInterval(AXIS_CHECK_INTERVAL);
    connect(m_axisCheckTimer, &QTimer::timeout, this, &LineTest::onAxisCheckTimer);
    
    m_stepTimer = new QTimer(this);
    m_stepTimer->setSingleShot(true);
    connect(m_stepTimer, &QTimer::timeout, this, &LineTest::processNextStep);
    
    // 初始化线段定义
    m_lineSegments.append(LineSegment(1000, 0, "第一段直线"));
    m_lineSegments.append(LineSegment(1000, 1000, "第二段直线"));
    m_lineSegments.append(LineSegment(0, 1000, "第三段直线"));
    m_lineSegments.append(LineSegment(0, 0, "第四段直线"));
}

LineTest::~LineTest()
{
    cleanup();
}

void LineTest::startTest()
{
    logMessage("开始ADMotion直线插补测试...");
    setState(TestState::Connecting);
    
    // 延迟一点开始，让日志先输出
    m_stepTimer->start(100);
}

void LineTest::setState(TestState newState)
{
    if (m_currentState != newState) {
        m_currentState = newState;
        
        QString stateStr;
        switch (newState) {
        case TestState::Idle: stateStr = "空闲"; break;
        case TestState::Connecting: stateStr = "连接中"; break;
        case TestState::WaitingInitStop: stateStr = "等待初始轴停止"; break;
        case TestState::SettingParameters: stateStr = "设置参数"; break;
        case TestState::ExecutingLine1: stateStr = "执行第一段直线"; break;
        case TestState::ExecutingLine2: stateStr = "执行第二段直线"; break;
        case TestState::ExecutingLine3: stateStr = "执行第三段直线"; break;
        case TestState::ExecutingLine4: stateStr = "执行第四段直线"; break;
        case TestState::Completed: stateStr = "完成"; break;
        case TestState::Error: stateStr = "错误"; break;
        }
        
        logMessage(QString("状态变更: %1").arg(stateStr));
    }
}

void LineTest::logMessage(const QString &message)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    qDebug() << QString("[%1] %2").arg(timestamp, message);
}

void LineTest::logError(const QString &error)
{
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss.zzz");
    qCritical() << QString("[%1] 错误: %2").arg(timestamp, error);
}

bool LineTest::connectToBoard()
{
    logMessage("1. 创建运动控制板卡句柄...");
    m_handle = API_CreateBoard();
    if (!m_handle) {
        logError("创建板卡句柄失败！");
        return false;
    }
    logMessage(QString("板卡句柄创建成功: %1").arg(reinterpret_cast<quintptr>(m_handle), 0, 16));
    
    logMessage("2. 连接运动控制板卡...");
    logMessage(QString("连接参数: IP=%1, 端口=%2").arg(m_ip).arg(m_port));
    
    short ret = API_OpenBoard(m_handle, m_ip.toLocal8Bit().data(), m_port);
    if (ret != SUCCESS_CODE) {
        logError(QString("连接板卡失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage("板卡连接成功！");
    return true;
}

void LineTest::disconnectFromBoard()
{
    if (m_handle) {
        API_CloseBoard(m_handle);
        API_DeleteBoard(m_handle);
        m_handle = nullptr;
        logMessage("板卡连接已断开");
    }
}

bool LineTest::waitForAxesStop()
{
    logMessage("等待坐标系 0 的轴停止运动...");
    m_stepStartTime = QDateTime::currentDateTime();
    m_axisCheckCount = 0;
    
    // 启动轴状态检查定时器
    m_axisCheckTimer->start();
    return true; // 异步检查，通过定时器处理
}

bool LineTest::setCoordinateParameters()
{
    logMessage("4. 设置坐标系参数...");
    short crd = 0;
    
    short ret = API_SetCrdPrm(m_handle, crd, m_maxVel, m_maxAcc);
    if (ret != SUCCESS_CODE) {
        logError(QString("设置坐标系参数失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage(QString("坐标系参数设置成功！坐标系: %1, 最大速度: %2, 最大加速度: %3")
               .arg(crd).arg(m_maxVel).arg(m_maxAcc));
    return true;
}

bool LineTest::executeLineSegment(const LineSegment &segment)
{
    logMessage(QString("\n=== 执行%1 ===").arg(segment.name));
    logMessage(QString("目标坐标: (%1, %2), 速度: %3, 加速度: %4")
               .arg(segment.x).arg(segment.y).arg(m_lineVel).arg(m_lineAcc));
    
    // 发送直线插补指令
    short ret = API_Ln(m_handle, 0, segment.x, segment.y, m_lineVel, m_lineAcc);
    if (ret != SUCCESS_CODE) {
        logError(QString("发送直线插补指令失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage("直线插补指令发送成功");
    
    // 启动坐标系插补
    ret = API_CrdStart(m_handle, 0);
    if (ret != SUCCESS_CODE) {
        logError(QString("启动坐标系插补失败！错误码: %1").arg(ret));
        return false;
    }
    logMessage("坐标系插补启动成功");
    
    return true;
}

void LineTest::cleanup()
{
    if (m_axisCheckTimer) {
        m_axisCheckTimer->stop();
    }
    if (m_stepTimer) {
        m_stepTimer->stop();
    }
    disconnectFromBoard();
}

void LineTest::finishTest(bool success)
{
    cleanup();
    
    if (success) {
        logMessage("\n=== 所有直线插补测试完成！===");
        logMessage("测试结果: 成功完成四段直线插补");
        setState(TestState::Completed);
    } else {
        logMessage("\n=== 测试失败！===");
        setState(TestState::Error);
    }
    
    logMessage("资源清理完成");
    logMessage("程序即将退出...");
    
    // 延迟一点再发送完成信号，确保日志输出
    QTimer::singleShot(1000, this, &LineTest::testFinished);
}

bool LineTest::checkAxesStatus()
{
    short axis0Status = 0, axis1Status = 0;
    short axis0 = 0, axis1 = 1; // 坐标系0对应轴0,1
    
    short ret0 = API_GetAxisStatus(m_handle, axis0, axis0Status);
    short ret1 = API_GetAxisStatus(m_handle, axis1, axis1Status);
    
    if (ret0 != SUCCESS_CODE || ret1 != SUCCESS_CODE) {
        logError(QString("获取轴状态失败！轴%1返回码: %2, 轴%3返回码: %4")
                 .arg(axis0).arg(ret0).arg(axis1).arg(ret1));
        return false;
    }
    
    // 检查两个轴是否都到位且不在运动中
    bool axis0Stopped = (axis0Status & AXIS_STATUS_IN_POSITION) && !(axis0Status & AXIS_STATUS_MOVING);
    bool axis1Stopped = (axis1Status & AXIS_STATUS_IN_POSITION) && !(axis1Status & AXIS_STATUS_MOVING);
    
    m_axisCheckCount++;
    if (m_axisCheckCount % 10 == 0) {  // 每1秒打印一次状态
        qint64 elapsedMs = m_stepStartTime.msecsTo(QDateTime::currentDateTime());
        logMessage(QString("检查次数: %1, 轴%2状态: 0x%3, 轴%4状态: 0x%5 (已用时: %6ms)")
                   .arg(m_axisCheckCount)
                   .arg(axis0).arg(axis0Status, 0, 16)
                   .arg(axis1).arg(axis1Status, 0, 16)
                   .arg(elapsedMs));
    }
    
    if (axis0Stopped && axis1Stopped) {
        qint64 elapsedMs = m_stepStartTime.msecsTo(QDateTime::currentDateTime());
        logMessage(QString("坐标系 0 的轴已停止！用时: %1ms").arg(elapsedMs));
        return true;
    }
    
    return false;
}

QString LineTest::formatAxisStatus(short status)
{
    QStringList statusList;
    if (status & AXIS_STATUS_IN_POSITION) statusList << "到位";
    if (status & AXIS_STATUS_MOVING) statusList << "运动中";
    return statusList.join("|");
}

void LineTest::onAxisCheckTimer()
{
    // 检查超时
    if (m_axisCheckCount >= m_maxAxisCheckCount) {
        m_axisCheckTimer->stop();
        logError("等待轴停止超时！超时时间: 30秒");
        setState(TestState::Error);
        finishTest(false);
        return;
    }

    // 检查轴状态
    if (checkAxesStatus()) {
        m_axisCheckTimer->stop();
        // 轴已停止，继续下一步
        m_stepTimer->start(STEP_DELAY);
    }
}

void LineTest::processNextStep()
{
    switch (m_currentState) {
    case TestState::Connecting:
        if (connectToBoard()) {
            setState(TestState::WaitingInitStop);
            logMessage("3. 等待初始轴停止...");
            waitForAxesStop();
        } else {
            setState(TestState::Error);
            finishTest(false);
        }
        break;

    case TestState::WaitingInitStop:
        if (setCoordinateParameters()) {
            setState(TestState::ExecutingLine1);
            logMessage("5. 开始执行四段直线插补...");
            m_currentSegmentIndex = 0;
            if (executeLineSegment(m_lineSegments[m_currentSegmentIndex])) {
                waitForAxesStop();
            } else {
                setState(TestState::Error);
                finishTest(false);
            }
        } else {
            setState(TestState::Error);
            finishTest(false);
        }
        break;

    case TestState::ExecutingLine1:
        logMessage(QString("%1 执行完成！").arg(m_lineSegments[m_currentSegmentIndex].name));
        setState(TestState::ExecutingLine2);
        m_currentSegmentIndex = 1;
        if (executeLineSegment(m_lineSegments[m_currentSegmentIndex])) {
            waitForAxesStop();
        } else {
            setState(TestState::Error);
            finishTest(false);
        }
        break;

    case TestState::ExecutingLine2:
        logMessage(QString("%1 执行完成！").arg(m_lineSegments[m_currentSegmentIndex].name));
        setState(TestState::ExecutingLine3);
        m_currentSegmentIndex = 2;
        if (executeLineSegment(m_lineSegments[m_currentSegmentIndex])) {
            waitForAxesStop();
        } else {
            setState(TestState::Error);
            finishTest(false);
        }
        break;

    case TestState::ExecutingLine3:
        logMessage(QString("%1 执行完成！").arg(m_lineSegments[m_currentSegmentIndex].name));
        setState(TestState::ExecutingLine4);
        m_currentSegmentIndex = 3;
        if (executeLineSegment(m_lineSegments[m_currentSegmentIndex])) {
            waitForAxesStop();
        } else {
            setState(TestState::Error);
            finishTest(false);
        }
        break;

    case TestState::ExecutingLine4:
        logMessage(QString("%1 执行完成！").arg(m_lineSegments[m_currentSegmentIndex].name));
        setState(TestState::Completed);
        finishTest(true);
        break;

    default:
        logError("未知状态，测试终止");
        setState(TestState::Error);
        finishTest(false);
        break;
    }
}

#include "linetest.moc"
