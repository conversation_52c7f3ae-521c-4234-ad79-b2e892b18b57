/**
 * @file linetest.h
 * @brief 直线插补测试类定义
 * <AUTHOR> Team
 * @date 2024-12-19
 */

#ifndef LINETEST_H
#define LINETEST_H

#include <QObject>
#include <QTimer>
#include <QString>
#include <QDateTime>
#include <QDebug>

// 包含ADMotion头文件
#include "../admc_pci.h"

class LineTest : public QObject
{
    Q_OBJECT

public:
    explicit LineTest(QObject *parent = nullptr);
    ~LineTest();

    // 启动测试
    void startTest();

signals:
    void testFinished();

private slots:
    void onAxisCheckTimer();
    void processNextStep();

private:
    // 测试状态枚举
    enum class TestState {
        Idle,               // 空闲
        Connecting,         // 连接中
        WaitingInitStop,    // 等待初始轴停止
        EnablingAxes,       // 使能轴
        SettingParameters,  // 设置参数
        ExecutingLine1,     // 执行第一段直线
        ExecutingLine2,     // 执行第二段直线
        ExecutingLine3,     // 执行第三段直线
        ExecutingLine4,     // 执行第四段直线
        Completed,          // 完成
        Error               // 错误
    };

    // 线段信息结构
    struct LineSegment {
        int32_t x;
        int32_t y;
        QString name;
        
        LineSegment(int32_t x_val, int32_t y_val, const QString &name_val)
            : x(x_val), y(y_val), name(name_val) {}
    };

    // 内部方法
    void setState(TestState newState);
    void logMessage(const QString &message);
    void logError(const QString &error);
    
    bool connectToBoard();
    void disconnectFromBoard();
    bool waitForAxesStop();
    bool enableAxes();
    bool setCoordinateParameters();
    bool executeLineSegment(const LineSegment &segment);
    void cleanup();
    void finishTest(bool success);
    
    // 轴状态检查
    bool checkAxesStatus();
    QString formatAxisStatus(short status);
    
    // 状态变量
    TestState m_currentState;
    TADMotionConn* m_handle;
    
    // 测试参数
    QString m_ip;
    int m_port;
    double m_maxVel;
    double m_maxAcc;
    double m_lineVel;
    double m_lineAcc;
    
    // 线段定义
    QList<LineSegment> m_lineSegments;
    int m_currentSegmentIndex;
    
    // 定时器和计数器
    QTimer *m_axisCheckTimer;
    QTimer *m_stepTimer;
    QDateTime m_stepStartTime;
    int m_axisCheckCount;
    int m_maxAxisCheckCount;
    
    // 常量定义
    static const short AXIS_STATUS_IN_POSITION = 1 << 4;  // 到位状态位 (16)
    static const short AXIS_STATUS_MOVING = 1 << 5;       // 运动中状态位 (32)
    static const short SUCCESS_CODE = 0;                  // 成功返回码
    static const int AXIS_CHECK_INTERVAL = 100;           // 轴状态检查间隔(ms)
    static const int STEP_DELAY = 500;                    // 步骤间延迟(ms)
};

#endif // LINETEST_H
