QT += core
QT -= gui

CONFIG += c++17 console
CONFIG -= app_bundle

TARGET = test_interpolation_qt
TEMPLATE = app

# 定义应用程序信息
VERSION = 1.0.0
QMAKE_TARGET_COMPANY = "ADMotion Team"
QMAKE_TARGET_PRODUCT = "ADMotion Line Interpolation Test"
QMAKE_TARGET_DESCRIPTION = "ADMotion直线插补测试程序(Qt控制台版)"
QMAKE_TARGET_COPYRIGHT = "Copyright (c) 2024 ADMotion Team"

# 源文件
SOURCES += \
    main.cpp

# 头文件
HEADERS += \
    ../admc_pci.h

# 包含路径
INCLUDEPATH += ..

# 库文件链接
win32 {
    # Windows平台库文件
    LIBS += -L$$PWD -lADMotion
    LIBS += -lws2_32 -lwinmm

    # 确保DLL在输出目录
    CONFIG(debug, debug|release) {
        DESTDIR = debug
        TARGET_CUSTOM_EXT = _debug
    } else {
        DESTDIR = release
    }

    # 注意：需要手动复制ADMotion.dll到输出目录
    # QMAKE_POST_LINK += copy命令在某些环境下可能失败
    # 请手动复制ADMotion.dll到release/或debug/目录
}

# 编译器特定设置
gcc {
    QMAKE_CXXFLAGS += -Wall -Wextra
    QMAKE_CXXFLAGS_RELEASE += -O2
    QMAKE_CXXFLAGS_DEBUG += -g -O0
}

msvc {
    QMAKE_CXXFLAGS += /W3
    QMAKE_CXXFLAGS_RELEASE += /O2
    QMAKE_CXXFLAGS_DEBUG += /Od /Zi
}

