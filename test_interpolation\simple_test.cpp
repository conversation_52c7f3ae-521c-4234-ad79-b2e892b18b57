/**
 * @file simple_test.cpp
 * @brief 简化版直线插补测试
 * <AUTHOR> Team
 * 
 * 这是一个简化版的测试程序，用于快速验证直线插补功能
 */

#include <iostream>
#include <chrono>
#include <thread>
#include "../admc_pci.h"

#pragma comment(lib, "admc_pci.lib")

using namespace std;

// 简单的等待函数
bool waitAxesStop(TADMotionConn* handle, int timeoutSec = 30) {
    cout << "等待轴停止..." << endl;
    
    for (int i = 0; i < timeoutSec * 10; i++) {
        short status0 = 0, status1 = 0;
        
        if (API_GetAxisStatus(handle, 0, status0) == 0 && 
            API_GetAxisStatus(handle, 1, status1) == 0) {
            
            // 检查到位状态（第4位）
            if ((status0 & 16) && (status1 & 16)) {
                cout << "轴已停止" << endl;
                return true;
            }
        }
        
        this_thread::sleep_for(chrono::milliseconds(100));
        
        if (i % 10 == 0) {
            cout << "等待中... " << (i/10 + 1) << "s" << endl;
        }
    }
    
    cout << "等待超时！" << endl;
    return false;
}

int main() {
    cout << "=== 简化版直线插补测试 ===" << endl;
    
    // 1. 创建句柄
    TADMotionConn* handle = API_CreateBoard();
    if (!handle) {
        cout << "创建句柄失败" << endl;
        return -1;
    }
    cout << "句柄创建成功" << endl;
    
    // 2. 连接卡
    if (API_OpenBoard(handle, "192.168.2.2", 6666) != 0) {
        cout << "连接失败" << endl;
        API_DeleteBoard(handle);
        return -1;
    }
    cout << "连接成功" << endl;
    
    // 3. 等待轴停止
    if (!waitAxesStop(handle)) {
        cout << "初始等待失败" << endl;
        goto cleanup;
    }
    
    // 4. 设置坐标系参数
    if (API_SetCrdPrm(handle, 0, 3000.0, 20.0) != 0) {
        cout << "设置坐标系参数失败" << endl;
        goto cleanup;
    }
    cout << "坐标系参数设置成功" << endl;
    
    // 5. 执行四段直线
    struct LineSegment {
        int32_t x, y;
        const char* name;
    };
    
    LineSegment segments[] = {
        {1000, 0, "第一段"},
        {1000, 1000, "第二段"},
        {0, 1000, "第三段"},
        {0, 0, "第四段"}
    };
    
    for (int i = 0; i < 4; i++) {
        cout << "\n执行" << segments[i].name << ": 目标(" 
             << segments[i].x << ", " << segments[i].y << ")" << endl;
        
        // 发送直线指令
        if (API_Ln(handle, 0, segments[i].x, segments[i].y, 1000.0, 10.0) != 0) {
            cout << "发送直线指令失败" << endl;
            goto cleanup;
        }
        
        // 启动插补
        if (API_CrdStart(handle, 0) != 0) {
            cout << "启动插补失败" << endl;
            goto cleanup;
        }
        
        cout << "指令发送成功，等待完成..." << endl;
        
        // 等待完成
        if (!waitAxesStop(handle)) {
            cout << segments[i].name << " 执行失败" << endl;
            goto cleanup;
        }
        
        cout << segments[i].name << " 完成" << endl;
    }
    
    cout << "\n=== 所有测试完成！===" << endl;
    
cleanup:
    API_CloseBoard(handle);
    API_DeleteBoard(handle);
    cout << "资源已清理" << endl;
    
    cout << "按回车键退出..." << endl;
    cin.get();
    
    return 0;
}
