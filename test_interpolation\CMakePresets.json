{"version": 8, "configurePresets": [{"name": "gcc", "displayName": "GCC 14.2.0 x86_64-w64-mingw32", "description": "正在使用编译器: C = G:\\mingw64\\bin\\gcc.exe, CXX = G:\\mingw64\\bin\\g++.exe", "generator": "MinGW Makefiles", "binaryDir": "${sourceDir}/out/build/${presetName}", "cacheVariables": {"CMAKE_INSTALL_PREFIX": "${sourceDir}/out/install/${presetName}", "CMAKE_C_COMPILER": "G:/mingw64/bin/gcc.exe", "CMAKE_CXX_COMPILER": "G:/mingw64/bin/g++.exe", "CMAKE_BUILD_TYPE": "Debug"}}]}