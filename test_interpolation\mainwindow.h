/**
 * @file mainwindow.h
 * @brief 主窗口类定义
 * <AUTHOR> Team
 * @date 2024-12-19
 */

#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QPushButton>
#include <QTextEdit>
#include <QProgressBar>
#include <QLabel>
#include <QGroupBox>
#include <QLineEdit>
#include <QSpinBox>
#include <QDoubleSpinBox>
#include <QStatusBar>
#include <QMenuBar>
#include <QAction>
#include <QTimer>
#include <QMessageBox>
#include <QApplication>
#include <QCloseEvent>

#include "motioncontroller.h"

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;

private slots:
    // UI事件槽函数
    void onStartTestClicked();
    void onStopTestClicked();
    void onClearLogClicked();
    void onAboutClicked();
    
    // 运动控制器信号槽
    void onLogMessage(const QString &message);
    void onProgressChanged(int value);
    void onTestStateChanged(MotionController::TestState state);
    void onTestCompleted(bool success);
    void onErrorOccurred(const QString &error);

private:
    void setupUI();
    void setupMenuBar();
    void setupStatusBar();
    void setupConnections();
    void updateUIState(MotionController::TestState state);
    void appendLog(const QString &message);
    
    // UI组件
    QWidget *m_centralWidget;
    QVBoxLayout *m_mainLayout;
    
    // 控制面板
    QGroupBox *m_controlGroup;
    QGridLayout *m_controlLayout;
    QLineEdit *m_ipEdit;
    QSpinBox *m_portSpin;
    QDoubleSpinBox *m_maxVelSpin;
    QDoubleSpinBox *m_maxAccSpin;
    QDoubleSpinBox *m_lineVelSpin;
    QDoubleSpinBox *m_lineAccSpin;
    QPushButton *m_startButton;
    QPushButton *m_stopButton;
    
    // 状态显示
    QGroupBox *m_statusGroup;
    QVBoxLayout *m_statusLayout;
    QLabel *m_stateLabel;
    QProgressBar *m_progressBar;
    QLabel *m_progressLabel;
    
    // 日志显示
    QGroupBox *m_logGroup;
    QVBoxLayout *m_logLayout;
    QTextEdit *m_logEdit;
    QPushButton *m_clearLogButton;
    
    // 菜单和状态栏
    QAction *m_aboutAction;
    QAction *m_exitAction;
    QLabel *m_statusLabel;
    
    // 运动控制器
    MotionController *m_motionController;
    
    // 状态变量
    bool m_testRunning;
};

#endif // MAINWINDOW_H
