/**
 * @file motioncontroller.h
 * @brief 运动控制器类定义
 * <AUTHOR> Team
 * @date 2024-12-19
 */

#ifndef MOTIONCONTROLLER_H
#define MOTIONCONTROLLER_H

#include <QObject>
#include <QTimer>
#include <QString>
#include <QDateTime>
#include <QDebug>

// 包含ADMotion头文件
extern "C" {
#include "../admc_pci.h"
}

class MotionController : public QObject
{
    Q_OBJECT

public:
    // 测试状态枚举
    enum class TestState {
        Idle,               // 空闲
        Connecting,         // 连接中
        WaitingInitStop,    // 等待初始轴停止
        SettingParameters,  // 设置参数
        ExecutingLine1,     // 执行第一段直线
        ExecutingLine2,     // 执行第二段直线
        ExecutingLine3,     // 执行第三段直线
        ExecutingLine4,     // 执行第四段直线
        Completed,          // 完成
        Error,              // 错误
        Stopping            // 停止中
    };
    Q_ENUM(TestState)

    // 线段信息结构
    struct LineSegment {
        int32_t x;
        int32_t y;
        QString name;
        
        LineSegment(int32_t x_val, int32_t y_val, const QString &name_val)
            : x(x_val), y(y_val), name(name_val) {}
    };

    explicit MotionController(QObject *parent = nullptr);
    ~MotionController();

    // 公共接口
    void startTest(const QString &ip, int port, 
                   double maxVel, double maxAcc,
                   double lineVel, double lineAcc);
    void stopTest();
    
    // 获取当前状态
    TestState currentState() const { return m_currentState; }
    bool isRunning() const { return m_currentState != TestState::Idle && 
                                   m_currentState != TestState::Completed && 
                                   m_currentState != TestState::Error; }

signals:
    void logMessage(const QString &message);
    void progressChanged(int value);
    void testStateChanged(TestState state);
    void testCompleted(bool success);
    void errorOccurred(const QString &error);

private slots:
    void onAxisCheckTimer();
    void processNextStep();

private:
    // 内部方法
    void setState(TestState newState);
    void emitLog(const QString &message);
    void emitError(const QString &error);
    
    bool connectToBoard();
    void disconnectFromBoard();
    bool waitForAxesStop(int timeoutMs = 30000);
    bool setCoordinateParameters();
    bool executeLineSegment(const LineSegment &segment);
    void cleanup();
    
    // 轴状态检查
    bool checkAxesStatus();
    QString formatAxisStatus(short status);
    
    // 状态变量
    TestState m_currentState;
    TADMotionConn* m_handle;
    
    // 测试参数
    QString m_ip;
    int m_port;
    double m_maxVel;
    double m_maxAcc;
    double m_lineVel;
    double m_lineAcc;
    
    // 线段定义
    QList<LineSegment> m_lineSegments;
    int m_currentSegmentIndex;
    
    // 定时器和计数器
    QTimer *m_axisCheckTimer;
    QTimer *m_stepTimer;
    QDateTime m_stepStartTime;
    int m_axisCheckCount;
    int m_maxAxisCheckCount;
    
    // 常量定义
    static const short AXIS_STATUS_IN_POSITION = 1 << 4;  // 到位状态位 (16)
    static const short AXIS_STATUS_MOVING = 1 << 5;       // 运动中状态位 (32)
    static const short SUCCESS_CODE = 0;                  // 成功返回码
    static const int AXIS_CHECK_INTERVAL = 100;           // 轴状态检查间隔(ms)
    static const int STEP_DELAY = 500;                    // 步骤间延迟(ms)
};

#endif // MOTIONCONTROLLER_H
