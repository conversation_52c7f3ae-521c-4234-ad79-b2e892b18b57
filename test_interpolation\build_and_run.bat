@echo off
echo =================================
echo ADMotion 直线插补测试编译脚本
echo =================================

:: 设置编码为UTF-8
chcp 65001 > nul

:: 检查是否存在Visual Studio环境
where cl >nul 2>nul
if %errorlevel% neq 0 (
    echo 未找到Visual Studio编译器，尝试设置环境...
    
    :: 尝试找到并设置VS环境
    if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files (x86)\Microsoft Visual Studio\2017\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
        call "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    ) else (
        echo 错误：未找到Visual Studio安装，请手动设置编译环境
        pause
        exit /b 1
    )
)

:: 创建构建目录
if not exist build mkdir build
cd build

echo.
echo 1. 使用CMake生成项目文件...
cmake .. -G "Visual Studio 16 2019" -A x64
if %errorlevel% neq 0 (
    echo CMake生成失败，尝试使用默认生成器...
    cmake ..
    if %errorlevel% neq 0 (
        echo CMake生成失败！
        pause
        exit /b 1
    )
)

echo.
echo 2. 编译项目...
cmake --build . --config Release
if %errorlevel% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo.
echo 3. 编译完成！

:: 检查可执行文件
if exist "bin\Release\test_line_interpolation.exe" (
    set EXE_PATH=bin\Release\test_line_interpolation.exe
) else if exist "Release\test_line_interpolation.exe" (
    set EXE_PATH=Release\test_line_interpolation.exe
) else if exist "test_line_interpolation.exe" (
    set EXE_PATH=test_line_interpolation.exe
) else (
    echo 未找到可执行文件！
    pause
    exit /b 1
)

echo 可执行文件位置: %EXE_PATH%

:: 询问是否运行
echo.
set /p choice="是否立即运行测试程序？(y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 4. 运行测试程序...
    echo 注意：请确保已将DLL文件复制到可执行文件目录！
    echo.
    %EXE_PATH%
) else (
    echo.
    echo 编译完成！可执行文件位于: %EXE_PATH%
    echo 请手动复制DLL文件到可执行文件目录后运行。
)

echo.
echo 完成！
pause
