# ADMotion 直线插补测试程序

## 概述

这是一个用于测试ADMotion运动控制库直线插补功能的测试程序。程序会连接运动控制卡，执行四段直线插补运动，形成一个矩形路径。

## 功能特性

- ✅ 自动连接运动控制卡
- ✅ 智能等待轴停止（带超时保护）
- ✅ 设置坐标系插补参数
- ✅ 执行四段直线插补（矩形路径）
- ✅ 实时状态监控和打印
- ✅ 完整的错误处理
- ✅ 中文注释和输出

## 测试路径

程序执行以下四段直线插补：

```
(0,1000) ←------ (1000,1000)
   ↓                 ↑
   ↓                 ↑
(0,0)    -----→  (1000,0)
```

1. **第一段**: (0,0) → (1000,0)
2. **第二段**: (1000,0) → (1000,1000)  
3. **第三段**: (1000,1000) → (0,1000)
4. **第四段**: (0,1000) → (0,0)

## 编译和运行

### 前提条件

1. **硬件要求**:
   - ADMotion运动控制卡
   - 网络连接（IP: ***********, 端口: 6666）

2. **软件要求**:
   - Visual Studio 2017或更高版本（Windows）
   - CMake 3.10或更高版本
   - ADMotion库文件

### 编译步骤

#### 方法1: 使用MinGW直接编译 (推荐)

```bash
# 运行直接编译脚本
compile_direct.bat
```

或者手动编译：
```bash
# 编译主测试程序
g++ -std=c++17 -I.. -o test_line_interpolation.exe test_line_interpolation.cpp libADMotion.a -lws2_32 -lwinmm

# 编译简化测试程序
g++ -std=c++17 -I.. -o simple_test.exe simple_test.cpp libADMotion.a -lws2_32 -lwinmm
```

#### 方法2: 使用CMake + MinGW

```bash
# 使用MinGW专用脚本
build_mingw.bat
```

或者手动：
```bash
mkdir build
cd build
cmake .. -G "MinGW Makefiles"
mingw32-make
```

#### 方法3: 使用Visual Studio

1. 打开Visual Studio
2. 创建新的C++控制台项目
3. 将`test_line_interpolation.cpp`添加到项目中
4. 包含ADMotion头文件路径
5. 链接相应的.lib文件
6. 编译运行

### 运行前准备

1. **复制DLL文件**:
   将以下DLL文件复制到可执行文件同一目录：
   - `admc_pci.dll`
   - 其他依赖的DLL文件

2. **网络配置**:
   确保运动控制卡的IP地址为`***********`，端口为`6666`

3. **硬件连接**:
   确保运动控制卡正确连接并上电

### 运行程序

```bash
# 进入可执行文件目录
cd bin

# 运行测试程序
./test_line_interpolation.exe
```

## 程序参数

程序中的关键参数可以根据需要修改：

```cpp
// 连接参数
const char* ip = "***********";  // 控制卡IP地址
int port = 6666;                 // 控制卡端口

// 坐标系参数
short crd = 0;                   // 坐标系编号
double maxVel = 3000.0;          // 最大速度
double maxAcc = 20.0;            // 最大加速度

// 插补参数
double lineVel = 1000.0;         // 直线速度
double lineAcc = 10.0;           // 直线加速度

// 超时参数
int timeoutMs = 30000;           // 等待超时时间（30秒）
```

## 输出示例

```
=== ADMotion 直线插补测试程序 ===
测试内容: 四段直线插补（矩形路径）
坐标系: 0, 轴: 0和1
===============================

1. 创建运动控制板卡句柄...
板卡句柄创建成功: 0x12345678

2. 连接运动控制板卡...
连接参数: IP=***********, 端口=6666
板卡连接成功！

3. 等待初始轴停止...
等待坐标系 0 的轴停止运动...
坐标系 0 的轴已停止！用时: 150ms

4. 设置坐标系参数...
坐标系参数设置成功！坐标系: 0, 最大速度: 3000, 最大加速度: 20

5. 开始执行四段直线插补...

=== 执行第一段直线 ===
目标坐标: (1000, 0), 速度: 1000, 加速度: 10
直线插补指令发送成功
坐标系插补启动成功
等待坐标系 0 的轴停止运动...
坐标系 0 的轴已停止！用时: 1250ms
第一段直线 执行完成！

... (其他三段类似输出)

=== 所有直线插补测试完成！===
测试结果: 成功完成四段直线插补

6. 清理资源...
资源清理完成

程序执行完毕，按任意键退出...
```

## 故障排除

### 常见问题

1. **编译错误**:
   - **undefined reference错误**: 确保`libADMotion.a`文件在当前目录
   - **找不到头文件**: 检查`../admc_pci.h`路径是否正确
   - **MinGW编译器未找到**: 确保MinGW已安装并添加到PATH

2. **链接错误**:
   - 确保使用正确的库文件（MinGW用`.a`文件，MSVC用`.lib`文件）
   - 检查库文件是否与编译器架构匹配（32位/64位）

3. **运行时错误**:
   - **DLL未找到**: 确保`ADMotion.dll`在可执行文件同一目录
   - **连接失败**: 检查网络连接和IP地址配置
   - **轴状态异常**: 检查轴使能状态和限位开关

4. **功能问题**:
   - **插补失败**: 检查坐标系参数设置和运动范围
   - **轴不停止**: 检查轴状态位判断逻辑
   - **超时问题**: 调整等待超时时间

### 调试建议

1. 启用详细日志输出
2. 检查每个API调用的返回值
3. 监控轴状态变化
4. 使用示波器检查信号输出

## 扩展功能

可以基于此测试程序扩展以下功能：

- 圆弧插补测试
- 多坐标系插补测试
- 速度前瞻功能测试
- 位置比较输出测试
- 实时轨迹监控

## 技术支持

如有问题，请联系ADMotion技术支持团队。
