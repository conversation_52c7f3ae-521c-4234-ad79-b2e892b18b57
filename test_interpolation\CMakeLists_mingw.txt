# 专门用于MinGW编译的CMakeLists.txt
cmake_minimum_required(VERSION 3.10)

project(ADMotionLineInterpolationTest)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置编译器标志
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O2")

# 包含头文件目录
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/..)

# 创建可执行文件
add_executable(test_line_interpolation 
    test_line_interpolation.cpp
)

# 直接链接当前目录下的库文件
set(ADMOTION_LIB_PATH ${CMAKE_CURRENT_SOURCE_DIR}/libADMotion.a)

if(EXISTS ${ADMOTION_LIB_PATH})
    message(STATUS "Found ADMotion library: ${ADMOTION_LIB_PATH}")
    target_link_libraries(test_line_interpolation ${ADMOTION_LIB_PATH})
else()
    message(FATAL_ERROR "ADMotion library not found at: ${ADMOTION_LIB_PATH}")
    message(FATAL_ERROR "Please ensure libADMotion.a is in the current directory")
endif()

# 链接Windows系统库
target_link_libraries(test_line_interpolation 
    ws2_32
    winmm
)

# 设置输出目录
set_target_properties(test_line_interpolation PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# 打印构建信息
message(STATUS "Building ADMotion Line Interpolation Test with MinGW")
message(STATUS "C++ Standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "Build Type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Library Path: ${ADMOTION_LIB_PATH}")

# 创建简化版测试程序
add_executable(simple_test 
    simple_test.cpp
)

target_link_libraries(simple_test 
    ${ADMOTION_LIB_PATH}
    ws2_32
    winmm
)

set_target_properties(simple_test PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)
