# ADMotion 直线插补测试程序 (Qt版本)

## 概述

这是ADMotion直线插补测试程序的Qt控制台版本。程序保持了原有的测试流程，但使用Qt的事件循环和定时器机制，避免了阻塞式等待。

## 特性

- ✅ Qt 5.12.12 兼容
- ✅ C++17 标准
- ✅ 控制台应用程序（无GUI界面）
- ✅ 使用Qt定时器替代sleep，避免阻塞
- ✅ 信号槽机制处理异步操作
- ✅ 保持原有API调用流程
- ✅ 中文日志输出
- ✅ 完整的错误处理

## 文件结构

```
test_interpolation/
├── test_interpolation_qt.pro    # Qt项目文件
├── main.cpp                     # 程序入口
├── linetest.h                   # 测试类头文件
├── linetest.cpp                 # 测试类实现
├── build_qt.bat                 # Qt编译脚本
├── libADMotion.a               # 链接库文件
├── ADMotion.dll                # 运行时DLL
└── README_Qt.md                # 本文档
```

## 编译要求

### 软件要求
- Qt 5.12.12 或更高版本
- MinGW 7.3.0 或更高版本
- CMake 3.10+ (可选)

### 硬件要求
- ADMotion运动控制卡
- 网络连接（IP: ***********, 端口: 6666）

## 编译方法

### 方法1: 使用编译脚本 (推荐)

```cmd
build_qt.bat
```

### 方法2: 手动编译

```cmd
# 生成Makefile
qmake test_interpolation_qt.pro

# 编译
mingw32-make
```

### 方法3: 使用Qt Creator

1. 打开Qt Creator
2. 打开项目文件 `test_interpolation_qt.pro`
3. 配置构建套件（MinGW）
4. 构建项目

## 运行程序

编译成功后，运行可执行文件：

```cmd
# Release版本
release\test_interpolation_qt.exe

# 或Debug版本
debug\test_interpolation_qt.exe
```

## 程序流程

程序执行以下步骤：

1. **初始化Qt应用程序**
2. **创建测试对象**
3. **连接运动控制卡** (IP: ***********, 端口: 6666)
4. **等待初始轴停止** (使用QTimer异步检查)
5. **设置坐标系参数** (最大速度: 3000, 加速度: 20)
6. **执行四段直线插补**:
   - 第一段: (0,0) → (1000,0)
   - 第二段: (1000,0) → (1000,1000)
   - 第三段: (1000,1000) → (0,1000)
   - 第四段: (0,1000) → (0,0)
7. **清理资源并退出**

## Qt特有优势

### 1. 非阻塞操作
- 使用QTimer进行轴状态检查，避免阻塞主线程
- 异步状态机处理，响应更灵敏

### 2. 信号槽机制
- 清晰的事件驱动架构
- 松耦合的组件通信

### 3. 跨平台支持
- Qt的跨平台特性
- 统一的API接口

### 4. 内存管理
- Qt的父子对象自动内存管理
- 智能指针支持

## 输出示例

```
[14:30:15.123] 开始ADMotion直线插补测试...
[14:30:15.124] 状态变更: 连接中
[14:30:15.225] 1. 创建运动控制板卡句柄...
[14:30:15.226] 板卡句柄创建成功: 12345678
[14:30:15.227] 2. 连接运动控制板卡...
[14:30:15.228] 连接参数: IP=***********, 端口=6666
[14:30:15.350] 板卡连接成功！
[14:30:15.351] 状态变更: 等待初始轴停止
[14:30:15.352] 3. 等待初始轴停止...
[14:30:15.353] 等待坐标系 0 的轴停止运动...
[14:30:16.354] 检查次数: 10, 轴0状态: 0x10, 轴1状态: 0x10 (已用时: 1001ms)
[14:30:16.455] 坐标系 0 的轴已停止！用时: 1102ms
...
```

## 故障排除

### 编译问题

1. **qmake未找到**:
   ```
   错误：未找到qmake
   ```
   解决：确保Qt安装目录的bin文件夹在PATH中

2. **编译器未找到**:
   ```
   错误：未找到g++编译器
   ```
   解决：确保MinGW安装并添加到PATH

3. **库文件链接错误**:
   ```
   undefined reference to API_xxx
   ```
   解决：确保libADMotion.a文件在项目目录

### 运行问题

1. **DLL未找到**:
   ```
   无法启动此程序，因为计算机中丢失ADMotion.dll
   ```
   解决：确保ADMotion.dll在可执行文件同一目录

2. **连接失败**:
   ```
   连接板卡失败！错误码: xxx
   ```
   解决：检查网络连接和控制卡状态

## 与控制台版本的区别

| 特性 | 控制台版本 | Qt版本 |
|------|------------|--------|
| 阻塞方式 | std::this_thread::sleep_for | QTimer异步 |
| 输出方式 | std::cout | qDebug() |
| 错误处理 | 同步返回 | 信号槽异步 |
| 内存管理 | 手动管理 | Qt自动管理 |
| 扩展性 | 有限 | 易于扩展 |

## 技术支持

如有问题，请联系ADMotion技术支持团队。

---

**注意**: 此程序为测试版本，请在安全环境下运行，确保运动控制设备处于安全状态。
